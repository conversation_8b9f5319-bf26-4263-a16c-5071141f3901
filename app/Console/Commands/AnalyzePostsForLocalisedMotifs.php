<?php

namespace App\Console\Commands;

use App\LocalisedMotif;
use App\Post;
use Illuminate\Console\Command;

class AnalyzePostsForLocalisedMotifs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'posts:analyze-localised-motifs {--output=analysis_output.json}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze posts content to determine LocalisedMotifs connections (READ-ONLY)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== ANALYZING POSTS FOR LOCALISED MOTIFS (READ-ONLY) ===');
        $this->warn('This command will NOT modify any database data - it only reads and analyzes.');

        // Get all LocalisedMotifs for reference
        $localisedMotifs = LocalisedMotif::all();
        $this->info('Available LocalisedMotifs:');
        foreach ($localisedMotifs as $motif) {
            $this->line("  {$motif->id}: {$motif->title}");
        }
        $this->newLine();

        // Get all posts with English translations
        $posts = Post::with(['translations' => function($query) {
            $query->where('locale', 'en');
        }])->orderBy('id')->get();

        $this->info("Found {$posts->count()} posts to analyze");
        $this->newLine();

        $postsData = [];

        foreach ($posts as $post) {
            $englishTranslation = $post->translations->where('locale', 'en')->first();
            
            if (!$englishTranslation) {
                $this->warn("Post {$post->id}: No English translation - SKIPPING");
                continue;
            }

            // Prepare content for analysis
            $title = $englishTranslation->title ?? '';
            $metaTitle = $englishTranslation->meta_title ?? '';
            $metaDescription = $englishTranslation->meta_description ?? '';
            $content = $englishTranslation->content ?? '';
            
            // Clean content (remove HTML, shortcodes, etc.)
            $cleanContent = strip_tags($content);
            $cleanContent = preg_replace('/\[Photo id=\d+\]/', '', $cleanContent);
            $cleanContent = preg_replace('/\s+/', ' ', trim($cleanContent));
            
            // Truncate content if too long (keep first 800 chars for analysis)
            $contentPreview = strlen($cleanContent) > 800 ? substr($cleanContent, 0, 800) . '...' : $cleanContent;

            $postData = [
                'id' => $post->id,
                'title' => $title,
                'meta_title' => $metaTitle,
                'meta_description' => $metaDescription,
                'content_preview' => $contentPreview,
                'full_content' => $cleanContent
            ];
            
            $postsData[] = $postData;
            
            // Output for analysis
            $this->info("POST {$post->id}:");
            $this->line("Title: {$title}");
            $this->line("Meta Title: {$metaTitle}");
            $this->line("Meta Description: {$metaDescription}");
            $this->line("Content: {$contentPreview}");
            $this->line(str_repeat("-", 80));
            $this->newLine();
        }

        // Save to JSON file for easier processing
        $outputFile = $this->option('output');
        $jsonData = [
            'localised_motifs' => $localisedMotifs->toArray(),
            'posts' => $postsData,
            'analysis_instructions' => [
                'task' => 'Analyze each post and determine which LocalisedMotifs (max 2) it should be connected to',
                'options' => [
                    1 => 'Heraklion',
                    2 => 'Heraklion Airport', 
                    3 => 'Chania',
                    4 => 'Chania Airport',
                    5 => 'Rethymno',
                    6 => 'Agios Nikolaos'
                ],
                'criteria' => [
                    'Direct mentions of cities/locations',
                    'Airport references',
                    'Regional context (western Crete = Chania, eastern = Agios Nikolaos, etc.)',
                    'Tourism/travel context',
                    'Car rental pickup/dropoff locations'
                ]
            ]
        ];

        file_put_contents(storage_path($outputFile), json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        $this->info("Analysis data saved to: storage/{$outputFile}");
        $this->info("Total posts for analysis: " . count($postsData));
        $this->newLine();

        $this->info("Next steps:");
        $this->line("1. Review the post content above or in the JSON file");
        $this->line("2. For each post, determine which LocalisedMotifs (1-6) it should connect to (max 2)");
        $this->line("3. Use the results to generate the seeder with hardcoded connections");
        
        return 0;
    }
}
