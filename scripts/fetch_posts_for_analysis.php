<?php

/**
 * <PERSON><PERSON><PERSON> to fetch all posts and prepare them for LLM analysis
 * This will output all post content in a format suitable for analysis
 */

// This script is designed to be run via Docker artisan command
// Usage: docker-compose exec app php artisan tinker --execute="require 'scripts/fetch_posts_for_analysis_safe.php';"
// OR: Create an artisan command instead

echo "This script should be run via artisan command to ensure proper Docker database connection.\n";
echo "Please use the artisan command version instead.\n";
exit(1);

use App\Post;
use App\LocalisedMotif;

echo "=== FETCHING POSTS FOR LOCALISED MOTIFS ANALYSIS ===\n\n";

// Get all LocalisedMotifs for reference
$localisedMotifs = LocalisedMotif::all();
echo "Available LocalisedMotifs:\n";
foreach ($localisedMotifs as $motif) {
    echo "  {$motif->id}: {$motif->title}\n";
}
echo "\n" . str_repeat("=", 80) . "\n\n";

// Get all posts with English translations
$posts = Post::with(['translations' => function($query) {
    $query->where('locale', 'en');
}])->orderBy('id')->get();

echo "Found {$posts->count()} posts to analyze\n\n";

$postsData = [];

foreach ($posts as $post) {
    $englishTranslation = $post->translations->where('locale', 'en')->first();

    if (!$englishTranslation) {
        continue; // Skip posts without English translation
    }

    // Prepare content for analysis
    $title = $englishTranslation->title ?? '';
    $metaTitle = $englishTranslation->meta_title ?? '';
    $metaDescription = $englishTranslation->meta_description ?? '';
    $content = $englishTranslation->content ?? '';

    // Clean content (remove HTML, shortcodes, etc.)
    $cleanContent = strip_tags($content);
    $cleanContent = preg_replace('/\[Photo id=\d+\]/', '', $cleanContent);
    $cleanContent = preg_replace('/\s+/', ' ', trim($cleanContent));

    // Truncate content if too long (keep first 800 chars for analysis)
    $contentPreview = strlen($cleanContent) > 800 ? substr($cleanContent, 0, 800) . '...' : $cleanContent;

    $postData = [
        'id' => $post->id,
        'title' => $title,
        'meta_title' => $metaTitle,
        'meta_description' => $metaDescription,
        'content_preview' => $contentPreview,
        'full_content' => $cleanContent
    ];

    $postsData[] = $postData;

    // Output for analysis
    echo "POST {$post->id}:\n";
    echo "Title: {$title}\n";
    echo "Meta Title: {$metaTitle}\n";
    echo "Meta Description: {$metaDescription}\n";
    echo "Content: {$contentPreview}\n";
    echo str_repeat("-", 80) . "\n\n";
}

// Save to JSON file for easier processing
$jsonData = [
    'localised_motifs' => $localisedMotifs->toArray(),
    'posts' => $postsData,
    'analysis_instructions' => [
        'task' => 'Analyze each post and determine which LocalisedMotifs (max 2) it should be connected to',
        'options' => [
            1 => 'Heraklion',
            2 => 'Heraklion Airport',
            3 => 'Chania',
            4 => 'Chania Airport',
            5 => 'Rethymno',
            6 => 'Agios Nikolaos'
        ],
        'criteria' => [
            'Direct mentions of cities/locations',
            'Airport references',
            'Regional context (western Crete = Chania, eastern = Agios Nikolaos, etc.)',
            'Tourism/travel context',
            'Car rental pickup/dropoff locations'
        ]
    ]
];

file_put_contents(__DIR__ . '/posts_analysis_data.json', json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "Analysis data saved to: scripts/posts_analysis_data.json\n";
echo "Total posts for analysis: " . count($postsData) . "\n\n";

echo "Next steps:\n";
echo "1. Review the post content above\n";
echo "2. For each post, determine which LocalisedMotifs (1-6) it should connect to (max 2)\n";
echo "3. Use the generate_seeder_from_analysis.php script to create the final seeder\n";
