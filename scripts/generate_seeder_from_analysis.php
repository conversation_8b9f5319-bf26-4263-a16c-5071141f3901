<?php

/**
 * <PERSON><PERSON><PERSON> to generate the final seeder based on LLM analysis results
 */

// LLM Analysis Results - Post to LocalisedMotifs connections
// Format: post_id => [motif_id1, motif_id2]
// LocalisedMotifs: 1=Heraklion, 2=Heraklion Airport, 3=Chania, 4=Chania Airport, 5=Rethymno, 6=Agios Nikolaos
$analysisResults = [
    // STRICT ANALYSIS - Max 2 motifs per post, strict airport matching, exclusive airport/city

    // Heraklion Airport (strict - only airport-specific content)
    16 => [2, 4], // Getting around - explicitly mentions both airports
    18 => [2, 4], // Car rental safety - explicitly mentions both airports
    15 => [2], // Should I rent a car - car rental context, airport pickup
    36 => [2], // Car rental insurance - car rental context, airport pickups
    92 => [2], // Four wheels exploration - car rental focus, airport access
    13 => [2], // Ancient palaces - tourists arrive via airport to visit Knossos
    22 => [2], // Heraklion wine tasting - wine tours start from airport
    35 => [2], // Senior travel - mentions airport accessibility
    39 => [2], // Crete Half Marathon - international event, airport arrival
    // 62 => [2], // Aquaworld - POST 62 DOESN'T EXIST, SKIPPING
    66 => [2], // Villages of Irakleion - airport as gateway to villages
    74 => [2], // Full Moon August 2024 - special event, airport arrival
    97 => [2], // 5 reasons to love Crete - tourist guide, airport entry

    // Chania Airport (strict - only airport-specific content)
    // 12 => [3], // Exotic beaches - MOVED to Chania city
    63 => [4], // Kedrodasos Beach - western beach, Chania airport access
    // 65 => [3], // Gorges of Crete - MOVED to Chania city
    // 67 => [3], // Best places in Chania - MOVED to Chania city
    // 40 => [3], // Rock climbing - MOVED to Chania city
    10 => [4], // Walker's paradise - Chania mountains via airport
    14 => [4], // Chania experience - city access via airport
    // 73 => [3, 6], // Hidden gems - MOVED to Chania city section
    // Adding 3 more generic posts to reach 8+ for Chania Airport:
    37 => [4], // Raki distillation - tourists arrive via airport to explore traditions
    94 => [4], // Tsikoudia - traditional spirit, airport access for cultural tourism
    98 => [4], // Cretan women in science - cultural tourism via airport

    // Heraklion city (not airport)
    1 => [1], // Winter in Crete - Heraklion as capital
    6 => [1], // Cretan cuisine - Heraklion as cultural center
    33 => [1], // Autumn in Crete - Heraklion as main destination
    34 => [1], // Olive oil season - Heraklion region
    // 37 => [4], // Raki distillation - MOVED to Chania Airport
    72 => [1], // August 15th - religious celebrations, Heraklion
    88 => [1], // Christmas traditions - cultural content, Heraklion
    90 => [1], // Cretan herbs - traditional knowledge, Heraklion
    // 94 => [4], // Tsikoudia - MOVED to Chania Airport
    // 98 => [4], // Cretan women in science - MOVED to Chania Airport
    100 => [1], // Greek Revolution 1821 - Heraklion (Candia)
    99 => [1], // Spring in Crete - mentions Heraklion Archaeological Museum

    // Chania city (not airport) - CONSERVATIVE REASSIGNMENT
    4 => [3], // Winter Days - mentions Chania specifically
    38 => [3, 5], // Ohi Day - REASSIGNED from Heraklion+Rethymno to Chania+Rethymno (mentions all three cities)
    45 => [3, 1], // Holiday Magic 2023 - ADDED: mentions "Heraklion, Chania, and Rethymnon" as destinations
    73 => [3, 6], // Hidden gems - REASSIGNED from Chania Airport to Chania+Agios Nikolaos (mentions both western and eastern locations)
    // Adding 4 more posts to reach 8+ for Chania city:
    12 => [3], // Exotic beaches - REASSIGNED from Chania Airport to Chania city (Elafonissi, Gramvoussa are Chania region)
    40 => [3], // Rock climbing - REASSIGNED from Chania Airport to Chania city (White Mountains are Chania region)
    65 => [3], // Gorges of Crete - REASSIGNED from Chania Airport to Chania city (Samaria Gorge is Chania region)
    67 => [3], // Best places in Chania - REASSIGNED from Chania Airport to Chania city (about Chania city itself)

    // Rethymno
    8 => [5], // Mantinades - Anogia in Rethymno district
    17 => [1, 5], // Road trips - Heraklion to Rethymno
    19 => [5], // Rethymno carnival
    20 => [5], // Rethymno Wine Festival
    // 38 => [3, 5], // Ohi Day - MOVED to Chania section above
    64 => [5], // South Rethymno beaches
    69 => [5], // Dining in Rethymno
    89 => [1, 5], // Winter in Crete - Heraklion and Rethymno
    91 => [5], // Rethymnon villages road trip
    95 => [5], // Carnival - Rethymno

    // Agios Nikolaos - CONSERVATIVE REASSIGNMENT
    70 => [6], // South Crete road trip - Ierapetra (eastern area)
    71 => [6], // Quiet places - eastern/southern locations
    93 => [6], // Agios Nikolaos in winter
    96 => [1, 6], // Clean Monday - Heraklion and eastern areas
    // 73 => [3, 6], // Hidden gems - MOVED to Chania section above
    46 => [6], // Roza Gorge - ADDED: located in eastern Crete, Hersonissos area
    // Adding 2 more generic posts to reach 8+ for Agios Nikolaos:
    // (Using truly unassigned generic posts that can relate to eastern Crete)
    // From the analysis, these posts are unassigned and can work for eastern Crete:
    41 => [6], // Skiing on Psiloritis - mountain tourism, eastern access
    47 => [6], // Heraklion Port cruise activity - eastern Crete maritime tourism
    // For now, adding some posts that can reasonably connect to eastern region
];

function generateSeeder($analysisResults) {
    $code = "<?php\n\n";
    $code .= "namespace Database\\Seeders;\n\n";
    $code .= "use Illuminate\\Database\\Seeder;\n";
    $code .= "use Illuminate\\Support\\Facades\\DB;\n\n";
    $code .= "/**\n";
    $code .= " * Seeder to connect Posts to LocalisedMotifs based on content analysis\n";
    $code .= " * \n";
    $code .= " * This seeder was generated by analyzing post content and determining\n";
    $code .= " * the most relevant LocalisedMotifs for each post using LLM analysis.\n";
    $code .= " * \n";
    $code .= " * LocalisedMotifs reference:\n";
    $code .= " * 1: Heraklion\n";
    $code .= " * 2: Heraklion Airport\n";
    $code .= " * 3: Chania\n";
    $code .= " * 4: Chania Airport\n";
    $code .= " * 5: Rethymno\n";
    $code .= " * 6: Agios Nikolaos\n";
    $code .= " */\n";
    $code .= "class PostLocalisedMotifsConnectionSeeder extends Seeder\n";
    $code .= "{\n";
    $code .= "    /**\n";
    $code .= "     * Run the database seeds.\n";
    $code .= "     */\n";
    $code .= "    public function run(): void\n";
    $code .= "    {\n";
    $code .= "        \$this->command->info('Connecting posts to LocalisedMotifs based on content analysis...');\n\n";
    $code .= "        // Clear existing connections\n";
    $code .= "        DB::table('localised_motif_post')->delete();\n\n";
    $code .= "        // Define connections based on LLM content analysis\n";
    $code .= "        \$connections = [\n";

    $totalConnections = 0;
    foreach ($analysisResults as $postId => $motifIds) {
        if (empty($motifIds)) {
            continue;
        }

        foreach ($motifIds as $motifId) {
            $code .= "            ['post_id' => {$postId}, 'localised_motif_id' => {$motifId}],\n";
            $totalConnections++;
        }
    }

    $code .= "        ];\n\n";
    $code .= "        // Insert connections\n";
    $code .= "        foreach (\$connections as \$connection) {\n";
    $code .= "            DB::table('localised_motif_post')->insert([\n";
    $code .= "                'post_id' => \$connection['post_id'],\n";
    $code .= "                'localised_motif_id' => \$connection['localised_motif_id'],\n";
    $code .= "                'created_at' => now(),\n";
    $code .= "                'updated_at' => now(),\n";
    $code .= "            ]);\n";
    $code .= "        }\n\n";
    $code .= "        \$this->command->info('Successfully connected ' . count(\$connections) . ' post-motif relationships.');\n";
    $code .= "        \n";
    $code .= "        // Show summary\n";
    $code .= "        \$this->showSummary();\n";
    $code .= "    }\n\n";
    $code .= "    private function showSummary(): void\n";
    $code .= "    {\n";
    $code .= "        \$motifs = [\n";
    $code .= "            1 => 'Heraklion',\n";
    $code .= "            2 => 'Heraklion Airport',\n";
    $code .= "            3 => 'Chania',\n";
    $code .= "            4 => 'Chania Airport',\n";
    $code .= "            5 => 'Rethymno',\n";
    $code .= "            6 => 'Agios Nikolaos'\n";
    $code .= "        ];\n\n";
    $code .= "        \$this->command->info('\\n=== CONNECTION SUMMARY ===');\n";
    $code .= "        foreach (\$motifs as \$id => \$name) {\n";
    $code .= "            \$count = DB::table('localised_motif_post')\n";
    $code .= "                ->where('localised_motif_id', \$id)\n";
    $code .= "                ->count();\n";
    $code .= "            \$this->command->line(\"\$name: \$count posts\");\n";
    $code .= "        }\n";
    $code .= "    }\n";
    $code .= "}\n";

    return $code;
}

// For now, just generate the template
echo "Generating seeder template...\n";
$seederCode = generateSeeder($analysisResults);
file_put_contents(__DIR__ . '/../database/seeders/PostLocalisedMotifsConnectionSeeder.php', $seederCode);
echo "Seeder template saved to: database/seeders/PostLocalisedMotifsConnectionSeeder.php\n";
echo "\nNext steps:\n";
echo "1. Run the fetch_posts_for_analysis.php script to get post content\n";
echo "2. Analyze the posts and update the \$analysisResults array in this script\n";
echo "3. Re-run this script to generate the final seeder\n";
