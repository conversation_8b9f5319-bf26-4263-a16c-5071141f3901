<?php

/**
 * <PERSON><PERSON><PERSON> to analyze all posts and determine LocalisedMotif connections
 * This script will output the connections that should be made in the seeder
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Post;
use App\LocalisedMotif;
use Illuminate\Support\Facades\DB;

echo "=== POST ANALYSIS FOR LOCALISED MOTIFS ===\n\n";

// Get all LocalisedMotifs
$localisedMotifs = LocalisedMotif::all();
echo "Available LocalisedMotifs:\n";
foreach ($localisedMotifs as $motif) {
    echo "  {$motif->id}: {$motif->title}\n";
}
echo "\n";

// Get all posts with English translations
$posts = Post::with(['translations' => function($query) {
    $query->where('locale', 'en');
}])->get();

echo "Found {$posts->count()} posts to analyze\n\n";

$analysisResults = [];

foreach ($posts as $post) {
    $englishTranslation = $post->translations->where('locale', 'en')->first();
    
    if (!$englishTranslation) {
        echo "Post {$post->id}: No English translation - SKIPPING\n";
        continue;
    }

    // Prepare content for analysis
    $title = $englishTranslation->title ?? '';
    $metaTitle = $englishTranslation->meta_title ?? '';
    $metaDescription = $englishTranslation->meta_description ?? '';
    $content = $englishTranslation->content ?? '';
    
    // Clean content (remove HTML, shortcodes, etc.)
    $content = strip_tags($content);
    $content = preg_replace('/\[Photo id=\d+\]/', '', $content);
    $content = preg_replace('/\s+/', ' ', trim($content));
    
    // Truncate content if too long (keep first 500 chars for analysis)
    if (strlen($content) > 500) {
        $content = substr($content, 0, 500) . '...';
    }

    echo "=== POST {$post->id} ===\n";
    echo "Title: {$title}\n";
    echo "Meta Title: {$metaTitle}\n";
    echo "Meta Description: {$metaDescription}\n";
    echo "Content Preview: " . substr($content, 0, 200) . "...\n";
    echo "\nANALYSIS NEEDED:\n";
    echo "Based on this content, which LocalisedMotifs (max 2) should this post be connected to?\n";
    echo "Available options:\n";
    echo "  1: Heraklion\n";
    echo "  2: Heraklion Airport\n";
    echo "  3: Chania\n";
    echo "  4: Chania Airport\n";
    echo "  5: Rethymno\n";
    echo "  6: Agios Nikolaos\n";
    echo "\nPlease analyze the content and determine the most relevant locations.\n";
    echo "Consider:\n";
    echo "- Direct mentions of cities/locations\n";
    echo "- Airport references\n";
    echo "- Regional context (western Crete = Chania, eastern = Agios Nikolaos, etc.)\n";
    echo "- Tourism/travel context\n";
    echo "\n";
    
    // Store for manual analysis
    $analysisResults[] = [
        'post_id' => $post->id,
        'title' => $title,
        'meta_title' => $metaTitle,
        'meta_description' => $metaDescription,
        'content' => $content,
        'suggested_motifs' => [] // To be filled manually
    ];
    
    echo "Enter LocalisedMotif IDs (comma-separated, max 2, or 'none'): ";
    $input = trim(fgets(STDIN));
    
    if ($input === 'none' || $input === '') {
        echo "No motifs assigned to post {$post->id}\n\n";
        continue;
    }
    
    $motifIds = array_map('trim', explode(',', $input));
    $motifIds = array_filter($motifIds, function($id) {
        return is_numeric($id) && $id >= 1 && $id <= 6;
    });
    
    if (count($motifIds) > 2) {
        $motifIds = array_slice($motifIds, 0, 2);
        echo "Limited to first 2 motifs\n";
    }
    
    $analysisResults[count($analysisResults) - 1]['suggested_motifs'] = $motifIds;
    
    echo "Post {$post->id} will be connected to motifs: " . implode(', ', $motifIds) . "\n\n";
    
    // Ask if user wants to continue or generate seeder
    echo "Continue to next post? (y/n/generate): ";
    $continue = trim(fgets(STDIN));
    
    if ($continue === 'n' || $continue === 'generate') {
        break;
    }
}

// Generate seeder code
echo "\n=== GENERATING SEEDER CODE ===\n\n";

$seederCode = generateSeederCode($analysisResults);
file_put_contents(__DIR__ . '/generated_post_motifs_seeder.php', $seederCode);

echo "Seeder code generated and saved to: scripts/generated_post_motifs_seeder.php\n";
echo "You can copy this code to create your seeder file.\n\n";

function generateSeederCode($results) {
    $connections = [];
    
    foreach ($results as $result) {
        if (!empty($result['suggested_motifs'])) {
            foreach ($result['suggested_motifs'] as $motifId) {
                $connections[] = [
                    'post_id' => $result['post_id'],
                    'motif_id' => $motifId,
                    'title' => $result['title']
                ];
            }
        }
    }
    
    $code = "<?php\n\n";
    $code .= "namespace Database\\Seeders;\n\n";
    $code .= "use Illuminate\\Database\\Seeder;\n";
    $code .= "use Illuminate\\Support\\Facades\\DB;\n\n";
    $code .= "class PostLocalisedMotifsConnectionSeeder extends Seeder\n";
    $code .= "{\n";
    $code .= "    /**\n";
    $code .= "     * Run the database seeds.\n";
    $code .= "     * \n";
    $code .= "     * This seeder was generated by analyzing post content and determining\n";
    $code .= "     * the most relevant LocalisedMotifs for each post.\n";
    $code .= "     */\n";
    $code .= "    public function run(): void\n";
    $code .= "    {\n";
    $code .= "        \$this->command->info('Connecting posts to LocalisedMotifs...');\n\n";
    $code .= "        // Clear existing connections\n";
    $code .= "        DB::table('localised_motif_post')->delete();\n\n";
    $code .= "        // Define connections based on content analysis\n";
    $code .= "        \$connections = [\n";
    
    foreach ($connections as $connection) {
        $title = addslashes($connection['title']);
        $code .= "            // Post {$connection['post_id']}: {$title}\n";
        $code .= "            ['post_id' => {$connection['post_id']}, 'localised_motif_id' => {$connection['motif_id']}],\n";
    }
    
    $code .= "        ];\n\n";
    $code .= "        foreach (\$connections as \$connection) {\n";
    $code .= "            DB::table('localised_motif_post')->insert([\n";
    $code .= "                'post_id' => \$connection['post_id'],\n";
    $code .= "                'localised_motif_id' => \$connection['localised_motif_id'],\n";
    $code .= "                'created_at' => now(),\n";
    $code .= "                'updated_at' => now(),\n";
    $code .= "            ]);\n";
    $code .= "        }\n\n";
    $code .= "        \$this->command->info('Connected ' . count(\$connections) . ' post-motif relationships.');\n";
    $code .= "    }\n";
    $code .= "}\n";
    
    return $code;
}
